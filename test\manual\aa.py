import sys
import os
import time

# 尝试导入nrc_interface库
try:
    # 添加lib目录到Python路径
    sys.path.insert(0, os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'lib'))
    import nrc_interface
    HARDWARE_AVAILABLE = True
    print("✅ INEXBOT库导入成功")
except ImportError as e:
    print(f"⚠️  INEXBOT库导入失败: {e}")
    print("🔄 使用模拟模式进行测试...")
    HARDWARE_AVAILABLE = False

    # 创建模拟的nrc_interface模块
    class MockNrcInterface:
        SUCCESS = 0

        @staticmethod
        def connect_robot(ip, port):
            print(f"[模拟] 连接机械臂 {ip}:{port}")
            return 1  # 模拟成功的socket_fd

        @staticmethod
        def set_servo_poweron(socket_fd):
            print(f"[模拟] 伺服上电")
            return MockNrcInterface.SUCCESS

        @staticmethod
        def set_speed(socket_fd, speed):
            print(f"[模拟] 设置速度为 {speed}%")
            return MockNrcInterface.SUCCESS

        @staticmethod
        def set_current_coord(socket_fd, coord):
            print(f"[模拟] 设置坐标系为 {coord}")
            return MockNrcInterface.SUCCESS

        @staticmethod
        def set_servo_poweroff(socket_fd):
            print(f"[模拟] 伺服下电")
            return MockNrcInterface.SUCCESS

        @staticmethod
        def disconnect_robot(socket_fd):
            print(f"[模拟] 断开连接")
            return MockNrcInterface.SUCCESS

    nrc_interface = MockNrcInterface()


def test_nrc_robot_poweron():
    # 机械臂配置
    robot_ip = "************"  # 替换为实际IP
    robot_port = '6001'  # 替换为实际端口
    move_speed = 30  # 初始低速

    print("====== INEXBOT机械臂上电/下电测试 [新版本] ======")
    print(f"目标IP: {robot_ip}, 端口: {robot_port}")
    print(f"运行模式: {'硬件模式' if HARDWARE_AVAILABLE else '模拟模式'}")

    socket_fd = -1
    try:
        # 1. 连接机械臂
        print(f"\n1. 正在连接机械臂...")
        socket_fd = nrc_interface.connect_robot(robot_ip, robot_port)
        if socket_fd < 0:
            print("❌ 机械臂连接失败！请检查网络连接和机械臂状态")
            return False
        print("✅ 机械臂连接成功")

        # 2. 伺服上电
        print(f"\n2. 正在给伺服系统上电...")
        result = nrc_interface.set_servo_poweron(socket_fd)
        if result != nrc_interface.SUCCESS:
            print(f"❌ 伺服上电失败！错误码: {result}")
            return False
        print("✅ 伺服系统已成功上电")

        # 3. 设置速度
        print(f"\n3. 正在设置运行速度为 {move_speed}%...")
        result = nrc_interface.set_speed(socket_fd, move_speed)
        if result != nrc_interface.SUCCESS:
            print(f"❌ 速度设置失败！错误码: {result}")
            return False
        print(f"✅ 运行速度已设置为 {move_speed}%")

        # 4. 设置为关节坐标系
        print(f"\n4. 正在设置坐标系为关节坐标系...")
        result = nrc_interface.set_current_coord(socket_fd, 0)
        if result != nrc_interface.SUCCESS:
            print(f"❌ 坐标系设置失败！错误码: {result}")
            return False
        print("✅ 关节坐标系设置成功")

        print("\n🎉 ====== 机械臂上电和初始化测试完成 ======")
        print("✅ 机械臂已成功上电并完成基本设置！")
        print("✅ 机械臂现在处于就绪状态，可以进行运动控制")
        sys.stdout.flush()

        # 等待一段时间模拟使用
        print(f"\n⏱️  等待1秒模拟机械臂使用...")
        sys.stdout.flush()

        try:
            time.sleep(1)
            print("⏱️  等待完成")
            sys.stdout.flush()

            # 5. 伺服下电
            print(f"\n5. 正在给伺服系统下电...")
            sys.stdout.flush()
            result = nrc_interface.set_servo_poweroff(socket_fd)
            print(f"下电结果: {result}")
            sys.stdout.flush()

            if result != nrc_interface.SUCCESS:
                print(f"❌ 伺服下电失败！错误码: {result}")
                # 即使下电失败也继续断开连接
            else:
                print("✅ 伺服系统已成功下电")
            sys.stdout.flush()
        except Exception as inner_e:
            print(f"❌ 下电过程中发生异常: {inner_e}")
            sys.stdout.flush()

        print("\n🎉 ====== 机械臂上电/下电完整测试完成 ======")
        print("✅ 机械臂已成功完成上电->使用->下电的完整流程！")

    except Exception as e:
        print(f"❌ 发生错误: {str(e)}")
        import traceback
        traceback.print_exc()

    # 最后总是断开连接
    if socket_fd >= 0:
        print(f"\n6. 正在断开连接...")
        try:
            nrc_interface.disconnect_robot(socket_fd)
            print("✅ 已断开连接")
        except Exception as e:
            print(f"⚠️  断开连接时发生异常: {e}")


if __name__ == "__main__":
    test_nrc_robot_poweron()