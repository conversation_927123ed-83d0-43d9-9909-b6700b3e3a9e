import nrc_interface
import time

def move_robot_axis(socket_fd, axis, is_positive, duration, speed=30):
    """
    控制机械臂单个轴运动指定时间
    参数:
        socket_fd: 套接字描述符
        axis: 轴编号(1-6对应X/Y/Z/A/B/C)
        is_positive: 运动方向(True-正方向, False-负方向)
        duration: 运动时间(秒)
        speed: 运动速度百分比(默认30%)
    """
    axis_names = {1: "X轴", 2: "Y轴", 3: "Z轴", 4: "A轴", 5: "B轴", 6: "C轴"}
    direction = "正方向" if is_positive else "负方向"
    print(f"开始{axis_names[axis]} {direction}运动，持续时间 {duration}秒...")

    # 开始点动 (使用布尔值作为方向参数)
    result = nrc_interface.robot_start_jogging(socket_fd, axis, is_positive)
    if result != nrc_interface.SUCCESS:
        print(f"{axis_names[axis]}运动启动失败！错误码: {result}")
        return False

    # 保持运动指定时间
    time.sleep(duration)

    # 停止运动
    result = nrc_interface.robot_stop_jogging(socket_fd, axis)
    if result != nrc_interface.SUCCESS:
        print(f"{axis_names[axis]}停止失败！错误码: {result}")
        return False

    print(f"{axis_names[axis]}运动完成")
    return True


def test_nrc_robot_movement():
    # 机械臂配置
    robot_ip = "************"  # 替换为实际IP
    robot_port = '6001'  # 替换为实际端口
    move_duration = 3  # 单轴运动时间(秒)
    move_speed = 30  # 初始低速

    try:
        # 1. 连接机械臂
        print(f"正在连接机械臂，IP: {robot_ip} 端口: {robot_port}...")
        socket_fd = nrc_interface.connect_robot(robot_ip, robot_port)
        if socket_fd < 0:
            print("机械臂连接失败！")
            return False
        print("机械臂连接成功")

        # 2. 伺服上电
        print("正在给伺服系统上电...")
        if nrc_interface.set_servo_poweron(socket_fd) != nrc_interface.SUCCESS:
            print("伺服上电失败！")
            return False
        print("伺服系统已成功上电")

        # 3. 设置速度
        print(f"正在设置运行速度为 {move_speed}%...")
        if nrc_interface.set_speed(socket_fd, move_speed) != nrc_interface.SUCCESS:
            print("速度设置失败！")
            return False
        print(f"运行速度已设置为 {move_speed}%")

        # 4. 设置为关节坐标系
        print("正在设置坐标系为关节坐标系...")
        if nrc_interface.set_current_coord(socket_fd, 0) != nrc_interface.SUCCESS:
            print("坐标系设置失败！")
            return False
        print("关节坐标系设置成功")

        # 5. 运动测试
        print("\n====== 开始各轴运动测试 ======")

        # 测试所有轴的正负方向运动
        for axis in range(1, 7):  # 1-6对应X/Y/Z/A/B/C
            axis_name = {1: "X", 2: "Y", 3: "Z", 4: "A", 5: "B", 6: "C"}[axis]

            # 正方向运动
            print(f"\n--- {axis_name}轴正方向运动 ---")
            if not move_robot_axis(socket_fd, axis, True, move_duration):
                return False
            time.sleep(1)  # 运动间隔

            # 负方向运动
            print(f"\n--- {axis_name}轴负方向运动 ---")
            if not move_robot_axis(socket_fd, axis, False, move_duration):
                return False
            time.sleep(1)

        print("\n所有轴运动测试完成！")
        return True

    except Exception as e:
        print(f"发生错误: {str(e)}")
        return False
    finally:
        if 'socket_fd' in locals():
            print("\n正在断开连接...")
            nrc_interface.disconnect_robot(socket_fd)
            print("已断开连接")


if __name__ == "__main__":
    test_nrc_robot_movement()